/**
 * 移动端滚动性能优化工具
 * 解决iPhone手机滚动卡顿问题
 */

export interface ScrollOptimizationOptions {
  /** 主容器选择器 */
  containerSelector?: string;
  /** 图表容器选择器 */
  chartSelector?: string;
  /** 表格容器选择器 */
  tableSelector?: string;
  /** 滚动结束延迟时间 */
  scrollEndDelay?: number;
  /** 是否启用调试模式 */
  debug?: boolean;
}

export class MobileScrollOptimizer {
  private options: Required<ScrollOptimizationOptions>;
  private scrollTimer: NodeJS.Timeout | null = null;
  private isScrolling = false;
  private cleanup: (() => void)[] = [];

  constructor(options: ScrollOptimizationOptions = {}) {
    this.options = {
      containerSelector: options.containerSelector || '',
      chartSelector: options.chartSelector || '.mobile-chart-container',
      tableSelector: options.tableSelector || '.mobile-table-container',
      scrollEndDelay: options.scrollEndDelay || 150,
      debug: options.debug || false,
      ...options,
    };
  }

  /**
   * 初始化滚动优化
   */
  public init(): () => void {
    // 只在移动端启用
    if (typeof window === 'undefined' || window.innerWidth > 768) {
      return () => { };
    }

    this.log('初始化移动端滚动优化');

    const containerEl = this.options.containerSelector
      ? document.querySelector(this.options.containerSelector) as HTMLElement
      : document.body;

    const chartContainers = document.querySelectorAll(this.options.chartSelector);
    const tableContainers = document.querySelectorAll(this.options.tableSelector);

    // 滚动开始处理
    const handleScrollStart = () => {
      if (this.isScrolling) return;

      this.isScrolling = true;
      this.log('滚动开始 - 启用硬件加速');

      containerEl?.classList.add('scrolling');
      chartContainers.forEach(el => el.classList.add('chart-interacting'));
      tableContainers.forEach(el => el.classList.add('table-scrolling'));
    };

    // 滚动结束处理
    const handleScrollEnd = () => {
      if (this.scrollTimer) clearTimeout(this.scrollTimer);

      this.scrollTimer = setTimeout(() => {
        this.isScrolling = false;
        this.log('滚动结束 - 移除硬件加速');

        containerEl?.classList.remove('scrolling');
        chartContainers.forEach(el => el.classList.remove('chart-interacting'));
        tableContainers.forEach(el => el.classList.remove('table-scrolling'));
      }, this.options.scrollEndDelay);
    };

    // 滚动事件处理器
    const handleScroll = () => {
      handleScrollStart();
      handleScrollEnd();
    };

    // 触摸事件处理器 - 优化触摸响应
    const handleTouchStart = () => {
      this.log('触摸开始');
      handleScrollStart();
    };

    const handleTouchEnd = () => {
      this.log('触摸结束');
      handleScrollEnd();
    };

    // 添加事件监听器
    const scrollOptions = { passive: true };
    const touchOptions = { passive: true };

    window.addEventListener('scroll', handleScroll, scrollOptions);
    containerEl?.addEventListener('scroll', handleScroll, scrollOptions);
    containerEl?.addEventListener('touchstart', handleTouchStart, touchOptions);
    containerEl?.addEventListener('touchend', handleTouchEnd, touchOptions);

    // 存储清理函数
    this.cleanup.push(() => {
      window.removeEventListener('scroll', handleScroll);
      containerEl?.removeEventListener('scroll', handleScroll);
      containerEl?.removeEventListener('touchstart', handleTouchStart);
      containerEl?.removeEventListener('touchend', handleTouchEnd);
      if (this.scrollTimer) clearTimeout(this.scrollTimer);
    });

    // 返回清理函数
    return () => this.destroy();
  }

  /**
   * 销毁优化器
   */
  public destroy(): void {
    this.log('销毁滚动优化器');
    this.cleanup.forEach(fn => fn());
    this.cleanup = [];
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  }

  /**
   * 调试日志
   */
  private log(message: string): void {
    if (this.options.debug) {
      console.log(`[MobileScrollOptimizer] ${message}`);
    }
  }
}

/**
 * 创建移动端滚动优化实例的便捷函数
 */
export function createMobileScrollOptimizer(options?: ScrollOptimizationOptions): MobileScrollOptimizer {
  return new MobileScrollOptimizer(options);
}

/**
 * 安卓专用滚动优化选项
 */
export interface AndroidScrollOptimizationOptions extends ScrollOptimizationOptions {
  /** 是否启用安卓特殊优化 */
  enableAndroidOptimization?: boolean;
  /** 是否强制使用软件渲染 */
  forceSoftwareRendering?: boolean;
  /** 滚动防抖延迟 */
  scrollDebounceDelay?: number;
  /** 是否启用触摸优化 */
  enableTouchOptimization?: boolean;
}

/**
 * 安卓专用滚动优化器
 */
export class AndroidScrollOptimizer extends MobileScrollOptimizer {
  private androidOptions: Required<AndroidScrollOptimizationOptions>;
  private touchStartTime = 0;
  private lastTouchY = 0;
  private scrollVelocity = 0;
  private debounceTimer: NodeJS.Timeout | null = null;

  constructor(options: AndroidScrollOptimizationOptions = {}) {
    super(options);
    this.androidOptions = {
      ...this.options,
      enableAndroidOptimization: options.enableAndroidOptimization ?? true,
      forceSoftwareRendering: options.forceSoftwareRendering ?? isLowVersionAndroid(),
      scrollDebounceDelay: options.scrollDebounceDelay ?? 16,
      enableTouchOptimization: options.enableTouchOptimization ?? true,
    };
  }

  init(): () => void {
    if (!isAndroidDevice() || !this.androidOptions.enableAndroidOptimization) {
      return super.init();
    }

    this.log('初始化安卓专用滚动优化');

    const containerEl = this.androidOptions.containerSelector
      ? document.querySelector(this.androidOptions.containerSelector)
      : document.body;

    if (!containerEl) {
      this.log('未找到容器元素');
      return () => { };
    }

    // 应用安卓专用样式
    this.applyAndroidStyles(containerEl as HTMLElement);

    // 设置安卓专用事件监听器
    const cleanup = this.setupAndroidEventListeners(containerEl as HTMLElement);

    return () => {
      cleanup();
      this.removeAndroidStyles(containerEl as HTMLElement);
    };
  }

  private applyAndroidStyles(containerEl: HTMLElement): void {
    containerEl.classList.add('android-scroll-optimized');

    if (this.androidOptions.forceSoftwareRendering) {
      containerEl.classList.add('android-software-rendering');
    }

    // 应用安卓特定的CSS变量
    containerEl.style.setProperty('--android-scroll-debounce', `${this.androidOptions.scrollDebounceDelay}ms`);
  }

  private removeAndroidStyles(containerEl: HTMLElement): void {
    containerEl.classList.remove('android-scroll-optimized', 'android-software-rendering');
    containerEl.style.removeProperty('--android-scroll-debounce');
  }

  private setupAndroidEventListeners(containerEl: HTMLElement): () => void {
    const chartContainers = document.querySelectorAll(this.androidOptions.chartSelector);
    const tableContainers = document.querySelectorAll(this.androidOptions.tableSelector);

    // 安卓专用的滚动处理
    const handleAndroidScroll = this.debounce(() => {
      this.handleScrollOptimization(containerEl, chartContainers, tableContainers);
    }, this.androidOptions.scrollDebounceDelay);

    // 安卓专用的触摸处理
    const handleAndroidTouchStart = (e: TouchEvent) => {
      if (!this.androidOptions.enableTouchOptimization) return;

      this.touchStartTime = performance.now();
      this.lastTouchY = e.touches[0].clientY;

      // 预启用硬件加速
      containerEl.classList.add('android-touch-active');
    };

    const handleAndroidTouchMove = (e: TouchEvent) => {
      if (!this.androidOptions.enableTouchOptimization) return;

      const currentY = e.touches[0].clientY;
      const deltaY = currentY - this.lastTouchY;
      const currentTime = performance.now();
      const deltaTime = currentTime - this.touchStartTime;

      // 计算滚动速度
      this.scrollVelocity = Math.abs(deltaY / deltaTime);

      // 根据滚动速度调整优化策略
      if (this.scrollVelocity > 0.5) {
        containerEl.classList.add('android-fast-scroll');
      } else {
        containerEl.classList.remove('android-fast-scroll');
      }

      this.lastTouchY = currentY;
      handleAndroidScroll();
    };

    const handleAndroidTouchEnd = () => {
      if (!this.androidOptions.enableTouchOptimization) return;

      // 延迟移除优化类，避免滚动惯性时的卡顿
      setTimeout(() => {
        containerEl.classList.remove('android-touch-active', 'android-fast-scroll');
      }, 300);
    };

    // 添加事件监听器
    containerEl.addEventListener('scroll', handleAndroidScroll, { passive: true });
    containerEl.addEventListener('touchstart', handleAndroidTouchStart, { passive: true });
    containerEl.addEventListener('touchmove', handleAndroidTouchMove, { passive: true });
    containerEl.addEventListener('touchend', handleAndroidTouchEnd, { passive: true });

    return () => {
      containerEl.removeEventListener('scroll', handleAndroidScroll);
      containerEl.removeEventListener('touchstart', handleAndroidTouchStart);
      containerEl.removeEventListener('touchmove', handleAndroidTouchMove);
      containerEl.removeEventListener('touchend', handleAndroidTouchEnd);
    };
  }

  private handleScrollOptimization(
    containerEl: HTMLElement,
    chartContainers: NodeListOf<Element>,
    tableContainers: NodeListOf<Element>
  ): void {
    // 根据滚动速度应用不同的优化策略
    if (this.scrollVelocity > 1.0) {
      // 快速滚动时，减少渲染复杂度
      chartContainers.forEach(el => el.classList.add('android-chart-fast-scroll'));
      tableContainers.forEach(el => el.classList.add('android-table-fast-scroll'));
    } else {
      // 慢速滚动时，恢复正常渲染
      chartContainers.forEach(el => el.classList.remove('android-chart-fast-scroll'));
      tableContainers.forEach(el => el.classList.remove('android-table-fast-scroll'));
    }
  }

  private debounce(func: Function, delay: number): (...args: any[]) => void {
    return (...args: any[]) => {
      if (this.debounceTimer) clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => func.apply(this, args), delay);
    };
  }
}

/**
 * Vue组合式函数 - 移动端滚动优化
 */
export function useMobileScrollOptimization(options?: ScrollOptimizationOptions) {
  let optimizer: MobileScrollOptimizer | null = null;
  let cleanup: (() => void) | null = null;

  const init = () => {
    if (optimizer) return;

    optimizer = new MobileScrollOptimizer(options);
    cleanup = optimizer.init();
  };

  const destroy = () => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
    if (optimizer) {
      optimizer.destroy();
      optimizer = null;
    }
  };

  return {
    init,
    destroy,
  };
}

/**
 * Vue组合式函数 - 安卓专用滚动优化
 */
export function useAndroidScrollOptimization(options?: AndroidScrollOptimizationOptions) {
  let optimizer: AndroidScrollOptimizer | null = null;
  let cleanup: (() => void) | null = null;

  const init = () => {
    if (optimizer) return;

    optimizer = new AndroidScrollOptimizer(options);
    cleanup = optimizer.init();
  };

  const destroy = () => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
    if (optimizer) {
      optimizer.destroy();
      optimizer = null;
    }
  };

  return {
    init,
    destroy,
  };
}

/**
 * iOS专用滚动优化选项
 */
export interface IOSScrollOptimizationOptions extends ScrollOptimizationOptions {
  /** 是否启用iOS特殊优化 */
  enableIOSOptimization?: boolean;
  /** 是否启用触摸优化 */
  enableTouchOptimization?: boolean;
  /** 滚动防抖延迟 */
  scrollDebounceDelay?: number;
  /** 是否启用惯性滚动优化 */
  enableMomentumOptimization?: boolean;
}

/**
 * iOS专用滚动优化器
 */
export class IOSScrollOptimizer extends MobileScrollOptimizer {
  private iosOptions: Required<IOSScrollOptimizationOptions>;
  private touchStartTime = 0;
  private lastTouchY = 0;
  private scrollVelocity = 0;
  private debounceTimer: NodeJS.Timeout | null = null;
  private momentumTimer: NodeJS.Timeout | null = null;

  constructor(options: IOSScrollOptimizationOptions = {}) {
    super(options);
    this.iosOptions = {
      ...this.options,
      enableIOSOptimization: options.enableIOSOptimization ?? true,
      enableTouchOptimization: options.enableTouchOptimization ?? true,
      scrollDebounceDelay: options.scrollDebounceDelay ?? 16,
      enableMomentumOptimization: options.enableMomentumOptimization ?? true,
    };
  }

  init(): () => void {
    if (!isIOSDevice() || !this.iosOptions.enableIOSOptimization) {
      return super.init();
    }

    this.log('初始化iOS专用滚动优化');

    const containerEl = this.iosOptions.containerSelector
      ? document.querySelector(this.iosOptions.containerSelector)
      : document.body;

    if (!containerEl) {
      this.log('未找到容器元素');
      return () => { };
    }

    // 应用iOS专用样式
    this.applyIOSStyles(containerEl as HTMLElement);

    // 设置iOS专用事件监听器
    const cleanup = this.setupIOSEventListeners(containerEl as HTMLElement);

    return () => {
      cleanup();
      this.removeIOSStyles(containerEl as HTMLElement);
    };
  }

  private applyIOSStyles(containerEl: HTMLElement): void {
    containerEl.classList.add('ios-scroll-optimized');

    // 应用iOS特定的CSS变量
    containerEl.style.setProperty('--ios-scroll-debounce', `${this.iosOptions.scrollDebounceDelay}ms`);

    // iOS特有的滚动优化样式
    containerEl.style.setProperty('-webkit-overflow-scrolling', 'touch');
    containerEl.style.setProperty('overscroll-behavior', 'contain');
    containerEl.style.setProperty('-webkit-overscroll-behavior', 'contain');
  }

  private removeIOSStyles(containerEl: HTMLElement): void {
    containerEl.classList.remove('ios-scroll-optimized', 'ios-momentum-scrolling');
    containerEl.style.removeProperty('--ios-scroll-debounce');
    containerEl.style.removeProperty('-webkit-overflow-scrolling');
    containerEl.style.removeProperty('overscroll-behavior');
    containerEl.style.removeProperty('-webkit-overscroll-behavior');
  }

  private setupIOSEventListeners(containerEl: HTMLElement): () => void {
    const chartContainers = document.querySelectorAll(this.iosOptions.chartSelector);
    const tableContainers = document.querySelectorAll(this.iosOptions.tableSelector);

    // iOS专用的滚动处理
    const handleIOSScroll = this.debounce(() => {
      this.handleScrollOptimization(containerEl, chartContainers, tableContainers);
    }, this.iosOptions.scrollDebounceDelay);

    // iOS专用的触摸处理
    const handleIOSTouchStart = (e: TouchEvent) => {
      if (!this.iosOptions.enableTouchOptimization) return;

      this.touchStartTime = performance.now();
      this.lastTouchY = e.touches[0].clientY;

      // 预启用优化
      containerEl.classList.add('ios-touch-active');

      // 清除之前的惯性滚动定时器
      if (this.momentumTimer) {
        clearTimeout(this.momentumTimer);
        this.momentumTimer = null;
      }
    };

    const handleIOSTouchMove = (e: TouchEvent) => {
      if (!this.iosOptions.enableTouchOptimization) return;

      const currentY = e.touches[0].clientY;
      const deltaY = currentY - this.lastTouchY;
      const currentTime = performance.now();
      const deltaTime = currentTime - this.touchStartTime;

      // 计算滚动速度
      this.scrollVelocity = Math.abs(deltaY / deltaTime);

      // 根据滚动速度调整优化策略
      if (this.scrollVelocity > 0.3) {
        containerEl.classList.add('ios-fast-scroll');
      } else {
        containerEl.classList.remove('ios-fast-scroll');
      }

      this.lastTouchY = currentY;
      handleIOSScroll();
    };

    const handleIOSTouchEnd = () => {
      if (!this.iosOptions.enableTouchOptimization) return;

      // iOS惯性滚动优化
      if (this.iosOptions.enableMomentumOptimization && this.scrollVelocity > 0.5) {
        containerEl.classList.add('ios-momentum-scrolling');

        // 惯性滚动期间保持优化
        this.momentumTimer = setTimeout(() => {
          containerEl.classList.remove('ios-momentum-scrolling', 'ios-fast-scroll');
        }, 500);
      }

      // 延迟移除触摸优化类
      setTimeout(() => {
        containerEl.classList.remove('ios-touch-active');
        if (!containerEl.classList.contains('ios-momentum-scrolling')) {
          containerEl.classList.remove('ios-fast-scroll');
        }
      }, 100);
    };

    // 添加事件监听器
    containerEl.addEventListener('scroll', handleIOSScroll, { passive: true });
    containerEl.addEventListener('touchstart', handleIOSTouchStart, { passive: true });
    containerEl.addEventListener('touchmove', handleIOSTouchMove, { passive: true });
    containerEl.addEventListener('touchend', handleIOSTouchEnd, { passive: true });

    return () => {
      containerEl.removeEventListener('scroll', handleIOSScroll);
      containerEl.removeEventListener('touchstart', handleIOSTouchStart);
      containerEl.removeEventListener('touchmove', handleIOSTouchMove);
      containerEl.removeEventListener('touchend', handleIOSTouchEnd);

      if (this.momentumTimer) {
        clearTimeout(this.momentumTimer);
        this.momentumTimer = null;
      }
    };
  }

  private handleScrollOptimization(
    containerEl: HTMLElement,
    chartContainers: NodeListOf<Element>,
    tableContainers: NodeListOf<Element>
  ): void {
    // 根据滚动速度应用不同的优化策略
    if (this.scrollVelocity > 0.8) {
      // 快速滚动时，减少渲染复杂度
      chartContainers.forEach(el => el.classList.add('ios-chart-fast-scroll'));
      tableContainers.forEach(el => el.classList.add('ios-table-fast-scroll'));
    } else {
      // 慢速滚动时，恢复正常渲染
      chartContainers.forEach(el => el.classList.remove('ios-chart-fast-scroll'));
      tableContainers.forEach(el => el.classList.remove('ios-table-fast-scroll'));
    }
  }

  private debounce(func: Function, delay: number): (...args: any[]) => void {
    return (...args: any[]) => {
      if (this.debounceTimer) clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => func.apply(this, args), delay);
    };
  }
}

/**
 * Vue组合式函数 - iOS专用滚动优化
 */
export function useIOSScrollOptimization(options?: IOSScrollOptimizationOptions) {
  let optimizer: IOSScrollOptimizer | null = null;
  let cleanup: (() => void) | null = null;

  const init = () => {
    if (optimizer) return;

    optimizer = new IOSScrollOptimizer(options);
    cleanup = optimizer.init();
  };

  const destroy = () => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
    if (optimizer) {
      optimizer.destroy();
      optimizer = null;
    }
  };

  return {
    init,
    destroy,
  };
}

/**
 * 检测是否为移动设备
 */
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') return false;

  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
    window.innerWidth <= 768;
}

/**
 * 检测是否为iOS设备
 */
export function isIOSDevice(): boolean {
  if (typeof window === 'undefined') return false;

  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * 检测是否为安卓设备
 */
export function isAndroidDevice(): boolean {
  if (typeof window === 'undefined') return false;

  return /Android/i.test(navigator.userAgent);
}

/**
 * 获取安卓版本号
 */
export function getAndroidVersion(): number | null {
  if (!isAndroidDevice()) return null;

  const match = navigator.userAgent.match(/Android\s([0-9\.]*)/);
  return match ? parseFloat(match[1]) : null;
}

/**
 * 检测是否为低版本安卓（需要特殊优化）
 */
export function isLowVersionAndroid(): boolean {
  const version = getAndroidVersion();
  return version !== null && version < 8.0;
}

/**
 * 应用移动端滚动优化样式
 */
export function applyMobileScrollStyles(): void {
  if (!isMobileDevice()) return;

  const style = document.createElement('style');
  style.textContent = `
    /* 移动端滚动优化样式 */
    .mobile-scroll-optimized {
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: contain;
      -webkit-overscroll-behavior: contain;
      contain: layout style paint;
      touch-action: pan-y;
    }
    
    .mobile-scroll-optimized.scrolling {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: scroll-position;
    }
    
    .mobile-chart-optimized.chart-interacting {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: transform;
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    }
    
    .mobile-table-optimized.table-scrolling {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: scroll-position;
    }
    
    .mobile-table-optimized .ant-table-body::-webkit-scrollbar {
      display: none;
    }
  `;

  document.head.appendChild(style);
}
