<template>
  <div
    ref="chartRef"
    id="averageChart"
    :style="{ width, height }"
    class="mobile-chart-container"
  ></div>
</template>
<script setup lang="ts">
  import { watch, ref, Ref, PropType, nextTick } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { useDebounceFn } from '@vueuse/core';

  const props = defineProps({
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '100%',
    },
    data: {
      type: Array as PropType<any>,
      default: () => [],
    },
  });
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 当前选中的legend项
  const selectedLegend = ref<string[]>([]);

  // 防抖的图表更新函数
  const debouncedUpdateChart = useDebounceFn((options: any) => {
    setOptions(options);
  }, 100);

  // 更新axisName显示
  const updateAxisName = (selectedItems: any) => {
    const chartInstance = getInstance();
    if (!chartInstance || !props.data) return;

    // 重新设置图表选项，更新axisName的formatter
    const newOptions = {
      radar: [
        {
          indicator: props.data.indicator,
          radius: 80,
          axisName: {
            fontSize: 10,
            color: '#2F3133',
            formatter: (value: string) => {
              let zR: any;
              let jR: any;
              // 如果没有选中任何项或全选，显示原始的valueType数据
              props.data.valueType?.forEach((item: any) => {
                if (item.name === value) {
                  zR = item.zR;
                  jR = item.jR;
                }
              });
              // 根据当前选中的legend项来计算显示的值
              if (selectedItems[0] && selectedItems[1]) {
                return `${value}\n{a|${zR}}/{b|${jR}}`;
              } else if (selectedItems[0] && !selectedItems[1]) {
                return `${value}\n{a|${zR}}`;
              } else if (!selectedItems[0] && selectedItems[1]) {
                return `${value}\n{b|${jR}}`;
              } else if (!selectedItems[0] && !selectedItems[1]) {
                return `${value}`;
              }
            },
            rich: {
              a: {
                color: '#FF8C20',
                fontSize: 10,
              },
              b: {
                color: '#FFCF90',
                fontSize: 10,
              },
            },
          },
        },
      ],
    };

    chartInstance.setOption(newOptions, false);
  };

  watch(
    () => props.data,
    (newVal) => {
      if (newVal) {
        // 初始化选中的legend项（默认全选）
        selectedLegend.value = newVal.data?.map((item: any) => item.name) || [];

        debouncedUpdateChart({
          title: {
            text: '绩效平均分',
          },
          radar: [
            {
              indicator: newVal.indicator,
              radius: 80, // 缩放
              // center: ['50%', '50%'], // 位置
              axisName: {
                fontSize: 10,
                color: '#2F3133',
                formatter: (value: string) => {
                  let zR: any;
                  let jR: any;
                  console.log(newVal.valueType);

                  newVal.valueType?.forEach((item: any) => {
                    if (item.name === value) {
                      zR = item.zR;
                      jR = item.jR;
                    }
                  });
                  return `${value}\n{a|${zR || 0}}/{b|${jR || 0}}`;
                },
                rich: {
                  a: {
                    color: '#FF8C20',
                    fontSize: 10,
                  },
                  b: {
                    color: '#FFCF90',
                    fontSize: 10,
                  },
                },
              },
            },
          ],
          legend: {
            show: true,
            orient: 'horizontal',
            right: '1%',
            bottom: '3%',
            icon: 'circle',
            data: newVal.data?.map((item: any) => item.name) || [],
          },
          tooltip: {
            trigger: 'item',
            show: false,
          },
          series: [
            {
              data: newVal.data,
              type: 'radar',
              tooltip: {
                trigger: 'item',
              },
              itemStyle: {
                // 修改 color 函数返回类型为 ZRColor 支持的类型
                color: (params: any): string => {
                  const colors = ['#f39423', '#f9c78b'];
                  return colors[params.dataIndex % colors.length];
                },
              },
            },
          ] as any,
        });

        // 使用nextTick确保图表实例已经创建完成后再添加事件监听
        nextTick(() => {
          const chartInstance = getInstance();
          if (chartInstance) {
            // 移除之前的事件监听
            chartInstance.off('legendselectchanged');

            // 添加legend选择变化事件监听
            chartInstance.on('legendselectchanged', (params: any) => {
              console.log('Legend selection changed:', params);

              // 获取当前选中的legend项
              const selected: any = Object.values(params.selected);

              // 更新axisName显示
              updateAxisName(selected);
            });
          }
        });
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style scoped lang="less">
  .mobile-chart-container {
    /* 移动端图表优化 */
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform;
    /* 防止图表渲染时的闪烁 */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
</style>
