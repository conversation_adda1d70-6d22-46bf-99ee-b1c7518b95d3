/**
 * iOS移动端滚动性能优化样式
 * 专门针对iPhone设备的滚动卡顿问题进行优化
 */

/* iOS滚动容器基础优化 */
.ios-scroll-optimized {
  /* 启用iOS原生滚动 */
  -webkit-overflow-scrolling: touch;
  
  /* 防止iOS橡皮筋效果 */
  overscroll-behavior: contain;
  -webkit-overscroll-behavior: contain;
  
  /* 基础性能优化 */
  contain: layout style paint;
  
  /* 优化触摸响应 */
  touch-action: pan-y;
  
  /* 减少重绘和回流 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  
  /* 触摸激活状态 */
  &.ios-touch-active {
    /* 动态启用硬件加速 */
    will-change: scroll-position;
    
    /* 优化触摸延迟 */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
  
  /* 快速滚动状态 */
  &.ios-fast-scroll {
    /* 减少渲染复杂度 */
    contain: strict;
    
    /* 优化动画性能 */
    * {
      transition-duration: 0s !important;
      animation-duration: 0s !important;
    }
  }
  
  /* iOS惯性滚动状态 */
  &.ios-momentum-scrolling {
    /* 保持硬件加速 */
    will-change: scroll-position;
    
    /* 优化惯性滚动 */
    -webkit-overflow-scrolling: touch;
    
    /* 减少重绘 */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}

/* iOS图表容器优化 */
.ios-chart-container,
.mobile-chart-container {
  /* 基础优化 */
  contain: layout style paint;
  
  /* 图表交互优化 */
  &.chart-interacting,
  &.ios-touch-active {
    /* 动态启用硬件加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform;
    
    /* 减少重绘 */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
  
  /* 快速滚动时的图表优化 */
  &.ios-chart-fast-scroll {
    /* 降低图表渲染质量以提升性能 */
    image-rendering: optimizeSpeed;
    -webkit-image-rendering: optimizeSpeed;
    
    /* 减少图表动画 */
    * {
      transition-duration: 0s !important;
      animation-duration: 0s !important;
    }
    
    /* 简化图表渲染 */
    canvas {
      image-rendering: pixelated;
      -webkit-image-rendering: pixelated;
    }
  }
}

/* iOS表格容器优化 */
.ios-table-container,
.mobile-table-container {
  /* 基础滚动优化 */
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  
  /* 表格滚动优化 */
  &.table-scrolling,
  &.ios-touch-active {
    will-change: scroll-position;
  }
  
  /* 快速滚动时的表格优化 */
  &.ios-table-fast-scroll {
    /* 减少表格渲染复杂度 */
    contain: strict;
    
    /* 优化表格动画 */
    * {
      transition-duration: 0s !important;
      animation-duration: 0s !important;
    }
  }
  
  /* Ant Design表格特殊优化 */
  ::v-deep .ant-table-body {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    
    /* 隐藏滚动条以提升性能 */
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  /* 表格行优化 */
  ::v-deep .ant-table-row {
    /* 减少重绘 */
    contain: layout style;
    
    /* 快速滚动时简化渲染 */
    .ios-table-fast-scroll & {
      contain: strict;
    }
  }
}

/* iOS特定的媒体查询优化 */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  /* 高分辨率iOS设备优化 */
  .ios-scroll-optimized {
    /* 减少像素密度以提升性能 */
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  
  /* 图表在高分辨率设备上的优化 */
  .ios-chart-container,
  .mobile-chart-container {
    /* 限制图表分辨率 */
    canvas {
      max-width: 100%;
      height: auto;
    }
  }
}

/* iPhone X系列及以上设备的特殊优化 */
@media only screen 
  and (device-width: 375px) 
  and (device-height: 812px) 
  and (-webkit-device-pixel-ratio: 3),
  only screen 
  and (device-width: 414px) 
  and (device-height: 896px) 
  and (-webkit-device-pixel-ratio: 2),
  only screen 
  and (device-width: 414px) 
  and (device-height: 896px) 
  and (-webkit-device-pixel-ratio: 3) {
  
  /* iPhone X系列滚动优化 */
  .ios-scroll-optimized {
    /* 针对刘海屏的安全区域优化 */
    padding-bottom: env(safe-area-inset-bottom);
    
    /* 优化滚动性能 */
    &.ios-fast-scroll {
      /* 更激进的性能优化 */
      contain: strict;
      will-change: scroll-position;
    }
  }
}

/* iOS 15特殊优化 */
@supports (-webkit-touch-callout: none) {
  .ios-scroll-optimized {
    /* iOS 15的滚动优化 */
    scroll-behavior: auto;
    
    /* 禁用iOS 15的新滚动特性以提升性能 */
    overscroll-behavior-y: contain;
    -webkit-overscroll-behavior-y: contain;
    
    /* 优化触摸事件 */
    &.ios-touch-active {
      /* 减少触摸延迟 */
      -webkit-tap-highlight-color: transparent;
      tap-highlight-color: transparent;
    }
  }
}

/* 动画优化 */
@keyframes ios-scroll-fade {
  from {
    opacity: 0.8;
  }
  to {
    opacity: 1;
  }
}

/* 滚动指示器优化 */
.ios-scroll-indicator {
  position: fixed;
  top: 50%;
  right: 10px;
  width: 4px;
  height: 40px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 1000;
  
  &.visible {
    opacity: 1;
  }
}

/* 性能监控样式 */
.ios-performance-monitor {
  position: fixed;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  pointer-events: none;
  
  &.hidden {
    display: none;
  }
}
