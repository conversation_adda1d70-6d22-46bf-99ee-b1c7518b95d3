/**
 * 移动端表格滚动优化样式
 * 解决iPhone手机表格滚动卡顿问题
 */

/* 移动端表格容器基础优化 */
.mobile-table-container {
  /* 基础优化 */
  contain: layout style;
  
  @media (max-width: 768px) {
    /* 移动端滚动优化 */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    -webkit-overscroll-behavior: contain;
    
    /* 优化触摸响应 */
    touch-action: pan-x pan-y;
    
    /* 动态硬件加速 */
    &.table-scrolling {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: scroll-position;
    }

    /* Ant Design表格优化 */
    ::v-deep .ant-table {
      /* 表格基础优化 */
      .ant-table-content {
        /* 减少重绘 */
        contain: layout style paint;
        
        .ant-table-scroll {
          /* 滚动容器优化 */
          -webkit-overflow-scrolling: touch;
          
          .ant-table-body {
            /* 表格主体优化 */
            -webkit-overflow-scrolling: touch;
            
            /* 隐藏移动端滚动条 */
            &::-webkit-scrollbar {
              display: none;
              width: 0;
              height: 0;
            }
            
            /* 表格行优化 */
            .ant-table-row {
              /* 减少重绘和回流 */
              contain: layout style;
              
              /* 优化触摸响应 */
              &:active {
                background-color: rgba(0, 0, 0, 0.02);
              }
            }
            
            /* 表格单元格优化 */
            .ant-table-cell {
              /* 文本渲染优化 */
              text-rendering: optimizeSpeed;
              -webkit-font-smoothing: antialiased;
              
              /* 减少重绘 */
              contain: layout style;
            }
          }
        }
        
        /* 固定列优化 */
        .ant-table-fixed-left,
        .ant-table-fixed-right {
          .ant-table-body-outer {
            .ant-table-body-inner {
              -webkit-overflow-scrolling: touch;
              
              &::-webkit-scrollbar {
                display: none;
              }
            }
          }
        }
      }
      
      /* 表格头部优化 */
      .ant-table-thead {
        .ant-table-cell {
          /* 头部单元格优化 */
          contain: layout style;
          /* 减少头部重绘 */
          will-change: auto;
        }
      }
      
      /* 分页器优化 */
      .ant-pagination {
        /* 移动端分页器优化 */
        contain: layout style;
        
        .ant-pagination-item {
          /* 分页项优化 */
          contain: layout style;
          
          &:active {
            transform: scale(0.95);
          }
        }
      }
    }
    
    /* 表格加载状态优化 */
    .ant-spin-nested-loading {
      .ant-spin-container {
        /* 加载容器优化 */
        contain: layout style paint;
      }
      
      .ant-spin {
        /* 加载动画优化 */
        contain: layout style paint;
        
        .ant-spin-dot {
          /* 减少动画重绘 */
          will-change: transform;
        }
      }
    }
  }
}

/* 移动端表格滚动性能优化类 */
@media (max-width: 768px) {
  /* 高性能滚动模式 */
  .high-performance-scroll {
    /* 启用GPU加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: scroll-position;
    
    /* 优化滚动行为 */
    scroll-behavior: auto;
    -webkit-scroll-behavior: auto;
    
    /* 减少重绘区域 */
    contain: layout style paint;
  }
  
  /* 表格虚拟滚动优化 */
  .virtual-scroll-table {
    /* 虚拟滚动容器 */
    .virtual-scroll-container {
      /* 容器优化 */
      contain: strict;
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      
      /* 虚拟项优化 */
      .virtual-scroll-item {
        contain: layout style;
        will-change: transform;
      }
    }
  }
  
  /* 表格懒加载优化 */
  .lazy-load-table {
    /* 懒加载行 */
    .lazy-load-row {
      /* 占位符优化 */
      &.placeholder {
        contain: layout style;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading-shimmer 1.5s infinite;
      }
    }
  }
  
  /* 表格响应式优化 */
  .responsive-table {
    /* 小屏幕优化 */
    @media (max-width: 480px) {
      .ant-table {
        /* 极小屏幕表格优化 */
        font-size: 12px;
        
        .ant-table-cell {
          padding: 8px 4px;
        }
        
        .ant-table-thead .ant-table-cell {
          padding: 6px 4px;
        }
      }
    }
  }
}

/* 表格滚动动画 */
@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* iOS特殊优化 */
@supports (-webkit-touch-callout: none) {
  .mobile-table-container {
    @media (max-width: 768px) {
      /* iOS特有的滚动优化 */
      -webkit-overflow-scrolling: touch;
      
      /* 防止iOS橡皮筋效果 */
      overscroll-behavior-y: contain;
      -webkit-overscroll-behavior-y: contain;
      
      /* iOS表格优化 */
      ::v-deep .ant-table-body {
        /* iOS滚动优化 */
        -webkit-overflow-scrolling: touch;
        
        /* 防止iOS滚动卡顿 */
        &::-webkit-scrollbar {
          -webkit-appearance: none;
          width: 0;
          height: 0;
        }
      }
    }
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-table-container {
    @media (max-width: 768px) {
      /* 高DPI屏幕表格优化 */
      ::v-deep .ant-table {
        /* 文本渲染优化 */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        
        .ant-table-cell {
          /* 边框优化 */
          border-width: 0.5px;
        }
      }
    }
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .mobile-table-container {
    @media (max-width: 768px) {
      /* 深色模式表格优化 */
      ::v-deep .ant-table {
        /* 深色模式下的性能优化 */
        .ant-table-row {
          &:active {
            background-color: rgba(255, 255, 255, 0.05);
          }
        }
      }
    }
  }
}
