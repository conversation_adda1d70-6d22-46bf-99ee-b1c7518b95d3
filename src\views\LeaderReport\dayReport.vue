<template>
  <div id="dayReport">
    <div class="targetMange_Box">
      <!-- <h2 class="title">团队绩效报告（日报）</h2> -->
      <p class="time"
        ><img style="margin-right: 5px" src="../../assets/svg/time.svg" /> 2023-07-10</p
      >
      <div class="box-item" style="padding: 10px 20px">
        <img class="icon" src="../../assets/svg/all.svg" />
        <div class="chart-box">
          <Chart :data="chartData" width="100%" height="100%" />
        </div>
        <div class="table-box">
          <a-table
            :columns="totalColumns"
            :data-source="totalData"
            :pagination="false"
            rowKey="id"
            :border="true"
          >
          </a-table>
        </div>
      </div>
      <div class="box-item">
        <img class="icon" src="../../assets/svg/daibiao.svg" />
        <div class="table-box">
          <a-table
            :columns="rankColumns"
            :data-source="rankData"
            :pagination="false"
            rowKey="id"
            :border="true"
          >
          </a-table>
        </div>
      </div>
      <div class="box-item">
        <img class="icon" src="../../assets/svg/wenti.svg" />
        <div class="table-box">
          <a-table
            :columns="questionColumns"
            :data-source="questionData"
            :pagination="false"
            rowKey="id"
            :border="true"
          >
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import Chart from '../performanceReport/components/chart.vue';

  const chartData = ref([
    {
      value: [10, 90, 90, 90],
      name: '昨日',
    },
    {
      value: [20, 80, 95, 50],
      name: '今日',
    },
  ]);

  const totalColumns = ref([
    {
      title: '目标类型',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: 'KPI名称',
      dataIndex: 'yesterday',
      key: 'yesterday',
      align: 'center',
    },
    {
      title: '目标值',
      dataIndex: 'today',
      key: 'today',
      align: 'center',
    },
    {
      title: '团队平均',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '昨日',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '今日',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '趋势',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
  ]);
  const totalData = ref([
    {
      rate: 111,
    },
  ]);
  const rankColumns = ref([
    {
      title: '员工姓名',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: 'EDI工号',
      dataIndex: 'yesterday',
      key: 'yesterday',
      align: 'center',
    },
    {
      title: '所属辖区',
      dataIndex: 'today',
      key: 'today',
      align: 'center',
    },
    {
      title: '绩效总分（日）',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '全国排名',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '省区排名',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '日常拜访',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '完成率',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '贴柜培训',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '库存管理',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
    {
      title: '陈列打造',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
    },
  ]);
  const rankData = ref([
    {
      rate: 111,
    },
  ]);

  const questionColumns = ref([
    {
      title: '员工姓名',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: 'EDI工号',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '所属辖区',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '绩效总分（日）',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '全国排名',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '省区排名',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '未达标项',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '原因分析',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
  ]);

  const questionData = ref([
    {
      name: 1,
    },
  ]);
</script>

<style scoped lang="less">
  ::v-deep {
    .ant-table.ant-table-small .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
      margin: 0px !important;
    }
  }
  #dayReport {
    width: 100%;
    height: 100%;
    padding: 8px;
    background: url('../../assets/svg/ri_bg.svg') no-repeat center 0;
    background-size: cover;
    .targetMange_Box {
      width: 100%;
      height: 100%;
      padding: 10px;
      .title {
        font-size: 20px;
        text-align: center;
        margin-bottom: 20px;
        margin-right: 10px;
      }
      .time {
        width: 146px;
        height: 28px;
        color: var(--Color, #ff9201);

        /* 点文本-加粗/14pt bold */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        margin: 10vh auto 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 24px;
        background: var(---25, rgba(255, 255, 255, 0.25));
      }
      .subtitle {
        font-size: 16px;
        display: flex;
        background: #ffedd0;
        height: 30px;
        line-height: 30px;
        font-weight: bold;
        &::before {
          content: '';
          width: 4px;
          height: 100%;
          background: #f9c048;
          display: inline-block;
          margin-right: 10px;
        }
      }
      .chart-box {
        box-sizing: border-box;
        width: 600px;
        height: 100%;
        padding-left: 88px;
      }
      .table-box {
        flex: 1;
        height: 100%;
        margin-left: 20px;
      }
      .box-item {
        box-sizing: border-box;
        width: 100%;
        height: calc((100% - 10vh - 100px) / 3); // 扣除时间区域和边距
        display: flex;
        margin-top: 20px;
        overflow: hidden;
        border-radius: 24px;
        background: #fff;

        /* 下层投影 */
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
        padding: 52px 20px 10px;
        position: relative;
        .icon {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 100;
        }
      }
    }
  }
</style>
