/**
 * 安卓设备专用滚动优化样式
 * 解决安卓手机滑动卡顿问题
 */

/* 安卓设备检测 */
@supports (-webkit-appearance: none) {
  /* 安卓专用滚动容器优化 */
  .android-scroll-optimized {
    /* 基础滚动优化 */
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    
    /* 防止过度滚动 */
    overscroll-behavior: contain;
    -webkit-overscroll-behavior: contain;
    
    /* 优化触摸响应 */
    touch-action: pan-y;
    
    /* 减少重绘和回流 */
    contain: layout style;
    
    /* 安卓特有的滚动优化 */
    scroll-behavior: auto;
    -webkit-scroll-behavior: auto;
    
    /* 触摸激活状态 */
    &.android-touch-active {
      /* 启用硬件加速 */
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: scroll-position;
      
      /* 优化触摸延迟 */
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
    }
    
    /* 快速滚动状态 */
    &.android-fast-scroll {
      /* 快速滚动时减少渲染复杂度 */
      contain: strict;
      
      /* 优化滚动性能 */
      scroll-snap-type: none;
      -webkit-scroll-snap-type: none;
      
      /* 减少动画和过渡效果 */
      * {
        transition: none !important;
        animation: none !important;
      }
    }
    
    /* 软件渲染模式（低版本安卓） */
    &.android-software-rendering {
      /* 禁用硬件加速 */
      transform: none;
      -webkit-transform: none;
      will-change: auto;
      
      /* 优化软件渲染性能 */
      backface-visibility: visible;
      -webkit-backface-visibility: visible;
      
      /* 减少图层合成 */
      isolation: auto;
    }
  }
  
  /* 安卓图表容器优化 */
  .android-chart-container {
    /* 基础优化 */
    contain: layout style paint;
    
    /* 图表交互优化 */
    &.chart-interacting {
      /* 动态启用硬件加速 */
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: transform;
      
      /* 减少重绘 */
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    }
    
    /* 快速滚动时的图表优化 */
    &.android-chart-fast-scroll {
      /* 降低图表渲染质量以提升性能 */
      image-rendering: optimizeSpeed;
      -webkit-image-rendering: optimizeSpeed;
      
      /* 减少图表动画 */
      * {
        transition-duration: 0s !important;
        animation-duration: 0s !important;
      }
    }
  }
  
  /* 安卓表格容器优化 */
  .android-table-container {
    /* 基础表格优化 */
    contain: layout style;
    
    /* 表格滚动优化 */
    &.table-scrolling {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: scroll-position;
    }
    
    /* 快速滚动时的表格优化 */
    &.android-table-fast-scroll {
      /* 简化表格渲染 */
      .ant-table-cell {
        /* 减少文本渲染复杂度 */
        text-rendering: optimizeSpeed;
        -webkit-font-smoothing: subpixel-antialiased;
        
        /* 简化边框渲染 */
        border-style: solid;
        border-width: 1px;
      }
      
      /* 隐藏非关键元素 */
      .ant-table-row:nth-child(n+20) {
        visibility: hidden;
      }
    }
    
    /* Ant Design表格安卓优化 */
    ::v-deep .ant-table {
      /* 表格容器优化 */
      .ant-table-content {
        /* 减少重绘区域 */
        contain: layout style paint;
        
        .ant-table-scroll {
          /* 滚动容器优化 */
          -webkit-overflow-scrolling: touch;
          
          /* 安卓滚动条优化 */
          &::-webkit-scrollbar {
            width: 4px;
            height: 4px;
            background: transparent;
          }
          
          &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;
          }
          
          .ant-table-body {
            /* 表格主体优化 */
            -webkit-overflow-scrolling: touch;
            
            /* 表格行优化 */
            .ant-table-row {
              /* 减少重绘 */
              contain: layout style;
              
              /* 优化触摸反馈 */
              &:active {
                background-color: rgba(0, 0, 0, 0.02);
                transition: background-color 0.1s ease;
              }
            }
            
            /* 表格单元格优化 */
            .ant-table-cell {
              /* 文本渲染优化 */
              text-rendering: optimizeSpeed;
              -webkit-font-smoothing: antialiased;
              
              /* 减少重绘 */
              contain: layout style;
              
              /* 优化长文本处理 */
              word-break: break-word;
              overflow-wrap: break-word;
            }
          }
        }
      }
      
      /* 表格头部优化 */
      .ant-table-thead {
        /* 固定头部优化 */
        contain: layout style;
        
        .ant-table-cell {
          /* 头部单元格优化 */
          contain: layout style;
          will-change: auto;
        }
      }
    }
  }
  
  /* 安卓专用动画优化 */
  .android-animation-optimized {
    /* 减少动画复杂度 */
    * {
      /* 简化过渡效果 */
      transition-property: transform, opacity;
      transition-timing-function: ease-out;
      transition-duration: 0.2s;
      
      /* 优化动画性能 */
      animation-fill-mode: both;
      animation-timing-function: ease-out;
    }
    
    /* 快速滚动时禁用动画 */
    &.android-fast-scroll * {
      transition: none !important;
      animation: none !important;
    }
  }
  
  /* 安卓低版本特殊优化 */
  @media screen and (-webkit-min-device-pixel-ratio: 1) {
    .android-scroll-optimized.android-software-rendering {
      /* 低版本安卓优化 */
      .ant-table-cell {
        /* 简化边框 */
        border-width: 1px;
        border-style: solid;
      }
      
      /* 减少阴影效果 */
      .box-shadow,
      .ant-card,
      .ant-modal {
        box-shadow: none !important;
      }
      
      /* 简化圆角 */
      * {
        border-radius: 0 !important;
      }
    }
  }
  
  /* 高DPI安卓设备优化 */
  @media screen and (-webkit-min-device-pixel-ratio: 2) {
    .android-scroll-optimized {
      /* 高DPI优化 */
      .ant-table-cell {
        /* 优化高DPI文本渲染 */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        
        /* 优化高DPI边框 */
        border-width: 0.5px;
      }
    }
  }
  
  /* 安卓Chrome浏览器特殊优化 */
  @supports (-webkit-appearance: none) and (not (-moz-appearance: none)) {
    .android-scroll-optimized {
      /* Chrome特有优化 */
      scroll-behavior: smooth;
      -webkit-scroll-behavior: smooth;
      
      /* 优化Chrome滚动性能 */
      &.android-touch-active {
        scroll-snap-type: y proximity;
        -webkit-scroll-snap-type: y proximity;
      }
    }
  }
}

/* 安卓设备媒体查询优化 */
@media (max-width: 768px) and (orientation: portrait) {
  .android-scroll-optimized {
    /* 竖屏优化 */
    .ant-table-scroll {
      /* 优化竖屏滚动 */
      max-height: calc(100vh - 200px);
    }
  }
}

@media (max-width: 768px) and (orientation: landscape) {
  .android-scroll-optimized {
    /* 横屏优化 */
    .ant-table-scroll {
      /* 优化横屏滚动 */
      max-height: calc(100vh - 100px);
    }
  }
}
