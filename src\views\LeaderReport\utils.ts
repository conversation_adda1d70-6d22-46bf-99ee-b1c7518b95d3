/**
   * 获取当前周的周一到周日日期
   * @returns {Array} 包含周一到周日日期的数组，格式为 YYYY-MM-DD
   */
const getCurrentWeekDays = (time, type) => {
  const now = new Date(time);
  const nowDay = now.getDay(); // 0是周日，1-6是周一到周六

  // 计算当前周的周一日期
  const monday = new Date(now);
  monday.setDate(now.getDate() - (nowDay === 0 ? 6 : nowDay - 1));

  // 生成周一到周日的日期数组
  const weekDays = [];
  for (let i = 0; i < 7; i++) {
    const day = new Date(monday);
    day.setDate(monday.getDate() + i);

    // 格式化日期为 YYYY-MM-DD
    const year = day.getFullYear();
    const month = String(day.getMonth() + 1).padStart(2, '0');
    const date = String(day.getDate()).padStart(2, '0');

    weekDays.push(`${year}${type}${month}${type}${date}`);
  }

  return weekDays;
};

export { getCurrentWeekDays };
