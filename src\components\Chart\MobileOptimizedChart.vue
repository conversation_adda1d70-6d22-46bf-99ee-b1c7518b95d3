<template>
  <div 
    ref="chartContainer" 
    class="mobile-optimized-chart"
    :class="{ 'chart-loading': loading }"
  >
    <div 
      ref="chartRef" 
      class="chart-content"
      :style="{ width: width, height: height }"
    ></div>
    <div v-if="loading" class="chart-loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">图表加载中...</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import echarts from '/@/utils/lib/echarts';
import type { EChartsOption } from 'echarts';

interface Props {
  data: EChartsOption;
  width?: string;
  height?: string;
  loading?: boolean;
  theme?: 'light' | 'dark';
  renderer?: 'canvas' | 'svg';
  devicePixelRatio?: number;
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '300px',
  loading: false,
  theme: 'light',
  renderer: 'canvas',
  devicePixelRatio: undefined,
});

const chartRef = ref<HTMLElement>();
const chartContainer = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;
let resizeObserver: ResizeObserver | null = null;

// 移动端优化配置
const isMobile = () => window.innerWidth <= 768;
const isIOS = () => /iPad|iPhone|iPod/.test(navigator.userAgent);

// 防抖的resize函数
const debouncedResize = useDebounceFn(() => {
  if (chartInstance && !chartInstance.isDisposed()) {
    chartInstance.resize();
  }
}, 100);

// 初始化图表
const initChart = async () => {
  if (!chartRef.value || chartInstance) return;

  await nextTick();

  try {
    // 移动端优化配置
    const initOptions: any = {
      renderer: props.renderer,
    };

    // 移动端特殊优化
    if (isMobile()) {
      // iOS设备使用较低的设备像素比以提升性能
      if (isIOS()) {
        initOptions.devicePixelRatio = props.devicePixelRatio || 1;
      }
      
      // 移动端使用canvas渲染器以获得更好的性能
      initOptions.renderer = 'canvas';
    }

    chartInstance = echarts.init(chartRef.value, props.theme, initOptions);
    
    // 设置图表数据
    if (props.data) {
      setChartOption(props.data);
    }

    // 设置resize监听
    setupResizeListener();
    
  } catch (error) {
    console.error('图表初始化失败:', error);
  }
};

// 设置图表选项
const setChartOption = (option: EChartsOption) => {
  if (!chartInstance || chartInstance.isDisposed()) return;

  try {
    // 移动端优化选项
    const optimizedOption = isMobile() ? optimizeForMobile(option) : option;
    
    chartInstance.setOption(optimizedOption, true);
  } catch (error) {
    console.error('设置图表选项失败:', error);
  }
};

// 移动端图表优化
const optimizeForMobile = (option: EChartsOption): EChartsOption => {
  const optimized = { ...option };

  // 优化动画配置
  if (optimized.animation !== false) {
    optimized.animation = true;
    optimized.animationDuration = 300; // 减少动画时间
    optimized.animationEasing = 'cubicOut';
  }

  // 优化工具提示
  if (optimized.tooltip) {
    optimized.tooltip = {
      ...optimized.tooltip,
      trigger: 'item',
      confine: true, // 限制在图表区域内
      transitionDuration: 0.1,
    };
  }

  // 优化图例
  if (optimized.legend) {
    optimized.legend = {
      ...optimized.legend,
      animation: false, // 关闭图例动画
    };
  }

  // 优化系列数据
  if (optimized.series && Array.isArray(optimized.series)) {
    optimized.series = optimized.series.map((series: any) => ({
      ...series,
      animation: series.animation !== false,
      animationDuration: 300,
      // 减少数据点的视觉效果以提升性能
      symbolSize: series.symbolSize || 4,
      lineStyle: {
        ...series.lineStyle,
        width: series.lineStyle?.width || 2,
      },
    }));
  }

  return optimized;
};

// 设置resize监听器
const setupResizeListener = () => {
  if (!chartContainer.value) return;

  // 使用ResizeObserver监听容器大小变化
  resizeObserver = new ResizeObserver(() => {
    debouncedResize();
  });

  resizeObserver.observe(chartContainer.value);

  // 监听窗口resize事件
  window.addEventListener('resize', debouncedResize, { passive: true });
  
  // 监听设备方向变化
  window.addEventListener('orientationchange', () => {
    setTimeout(debouncedResize, 100);
  }, { passive: true });
};

// 清理资源
const cleanup = () => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }

  window.removeEventListener('resize', debouncedResize);
  window.removeEventListener('orientationchange', debouncedResize);

  if (chartInstance && !chartInstance.isDisposed()) {
    chartInstance.dispose();
    chartInstance = null;
  }
};

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (newData && chartInstance) {
      setChartOption(newData);
    }
  },
  { deep: true }
);

// 监听主题变化
watch(
  () => props.theme,
  () => {
    cleanup();
    nextTick(() => {
      initChart();
    });
  }
);

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  cleanup();
});

// 暴露方法给父组件
defineExpose({
  getChartInstance: () => chartInstance,
  resize: debouncedResize,
  refresh: () => {
    cleanup();
    nextTick(() => {
      initChart();
    });
  },
});
</script>

<style scoped lang="less">
.mobile-optimized-chart {
  position: relative;
  width: 100%;
  height: 100%;
  
  .chart-content {
    width: 100%;
    height: 100%;
  }
  
  .chart-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
    
    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .loading-text {
      margin-top: 12px;
      color: #666;
      font-size: 14px;
    }
  }
  
  /* 移动端优化 */
  @media (max-width: 768px) {
    /* 减少重绘和回流 */
    contain: layout style paint;
    
    /* 优化触摸响应 */
    touch-action: pan-y;
    
    .chart-content {
      /* 移动端图表优化 */
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
      
      /* 动态硬件加速 */
      &.chart-interacting {
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        will-change: transform;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
