/**
 * 移动端性能监控工具
 * 用于监控和调试移动端滚动性能问题
 */

export interface PerformanceMetrics {
  fps: number;
  scrollEvents: number;
  memoryUsage?: number;
  renderTime: number;
  scrollLatency: number;
  // 安卓特定指标
  androidVersion?: number;
  isLowEndDevice?: boolean;
  touchLatency?: number;
  scrollVelocity?: number;
  gpuAcceleration?: boolean;
}

export interface PerformanceConfig {
  enableFPSMonitor?: boolean;
  enableScrollMonitor?: boolean;
  enableMemoryMonitor?: boolean;
  enableRenderMonitor?: boolean;
  // 安卓特定监控选项
  enableAndroidMonitor?: boolean;
  enableTouchMonitor?: boolean;
  enableGPUMonitor?: boolean;
  sampleInterval?: number;
  maxSamples?: number;
  debug?: boolean;
}

export class MobilePerformanceMonitor {
  private config: Required<PerformanceConfig>;
  private metrics: PerformanceMetrics[] = [];
  private isMonitoring = false;
  private animationId: number | null = null;
  private lastFrameTime = 0;
  private frameCount = 0;
  private scrollEventCount = 0;
  private lastScrollTime = 0;
  private renderStartTime = 0;

  // 安卓特定监控属性
  private touchStartTime = 0;
  private lastScrollVelocity = 0;
  private isAndroidDevice = false;
  private androidVersion: number | null = null;
  private isLowEndDevice = false;

  constructor(config: PerformanceConfig = {}) {
    this.config = {
      enableFPSMonitor: config.enableFPSMonitor ?? true,
      enableScrollMonitor: config.enableScrollMonitor ?? true,
      enableMemoryMonitor: config.enableMemoryMonitor ?? true,
      enableRenderMonitor: config.enableRenderMonitor ?? true,
      enableAndroidMonitor: config.enableAndroidMonitor ?? true,
      enableTouchMonitor: config.enableTouchMonitor ?? true,
      enableGPUMonitor: config.enableGPUMonitor ?? true,
      sampleInterval: config.sampleInterval ?? 1000,
      maxSamples: config.maxSamples ?? 60,
      debug: config.debug ?? false,
    };

    // 初始化安卓设备检测
    this.initAndroidDetection();
  }

  /**
   * 初始化安卓设备检测
   */
  private initAndroidDetection(): void {
    this.isAndroidDevice = /Android/i.test(navigator.userAgent);

    if (this.isAndroidDevice) {
      // 获取安卓版本
      const match = navigator.userAgent.match(/Android\s([0-9\.]*)/);
      this.androidVersion = match ? parseFloat(match[1]) : null;

      // 检测是否为低端设备
      this.isLowEndDevice = this.detectLowEndDevice();

      if (this.config.debug) {
        console.log('[PerformanceMonitor] Android device detected:', {
          version: this.androidVersion,
          isLowEnd: this.isLowEndDevice,
          userAgent: navigator.userAgent,
        });
      }
    }
  }

  /**
   * 检测是否为低端安卓设备
   */
  private detectLowEndDevice(): boolean {
    // 基于硬件并发数判断
    const hardwareConcurrency = navigator.hardwareConcurrency || 1;

    // 基于内存判断（如果可用）
    const deviceMemory = (navigator as any).deviceMemory;

    // 基于安卓版本判断
    const isOldAndroid = this.androidVersion && this.androidVersion < 7.0;

    return hardwareConcurrency <= 2 ||
      (deviceMemory && deviceMemory <= 2) ||
      !!isOldAndroid;
  }

  /**
   * 开始性能监控
   */
  public start(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.log('开始性能监控');

    if (this.config.enableFPSMonitor) {
      this.startFPSMonitor();
    }

    if (this.config.enableScrollMonitor) {
      this.startScrollMonitor();
    }

    if (this.config.enableRenderMonitor) {
      this.startRenderMonitor();
    }

    // 启动安卓特定监控
    if (this.config.enableTouchMonitor && this.isAndroidDevice) {
      this.startTouchMonitor();
    }

    // 定期收集指标
    setInterval(() => {
      this.collectMetrics();
    }, this.config.sampleInterval);
  }

  /**
   * 停止性能监控
   */
  public stop(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    this.log('停止性能监控');

    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    this.removeEventListeners();
  }

  /**
   * 获取性能指标
   */
  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  /**
   * 获取安卓设备性能报告
   */
  public getAndroidPerformanceReport(): {
    deviceInfo: {
      isAndroid: boolean;
      version: number | null;
      isLowEnd: boolean;
      hardwareConcurrency: number;
      deviceMemory?: number;
    };
    performanceMetrics: PerformanceMetrics[];
    recommendations: string[];
  } {
    const deviceInfo = {
      isAndroid: this.isAndroidDevice,
      version: this.androidVersion,
      isLowEnd: this.isLowEndDevice,
      hardwareConcurrency: navigator.hardwareConcurrency || 1,
      deviceMemory: (navigator as any).deviceMemory,
    };

    const recommendations: string[] = [];

    if (this.isAndroidDevice) {
      if (this.isLowEndDevice) {
        recommendations.push('检测到低端设备，建议启用软件渲染模式');
        recommendations.push('减少动画效果和复杂的CSS变换');
        recommendations.push('限制同时渲染的元素数量');
      }

      if (this.androidVersion && this.androidVersion < 7.0) {
        recommendations.push('检测到较旧的安卓版本，建议禁用硬件加速');
        recommendations.push('使用简化的滚动优化策略');
      }

      const avgFps = this.metrics.length > 0
        ? this.metrics.reduce((sum, m) => sum + m.fps, 0) / this.metrics.length
        : 0;

      if (avgFps < 30) {
        recommendations.push('FPS过低，建议优化渲染性能');
        recommendations.push('考虑使用虚拟滚动或分页加载');
      }

      const avgScrollLatency = this.metrics.length > 0
        ? this.metrics.reduce((sum, m) => sum + m.scrollLatency, 0) / this.metrics.length
        : 0;

      if (avgScrollLatency > 100) {
        recommendations.push('滚动延迟过高，建议优化滚动事件处理');
        recommendations.push('使用防抖或节流技术');
      }
    }

    return {
      deviceInfo,
      performanceMetrics: this.getMetrics(),
      recommendations,
    };
  }

  /**
   * 获取平均性能指标
   */
  public getAverageMetrics(): PerformanceMetrics {
    if (this.metrics.length === 0) {
      return {
        fps: 0,
        scrollEvents: 0,
        memoryUsage: 0,
        renderTime: 0,
        scrollLatency: 0,
      };
    }

    const sum = this.metrics.reduce(
      (acc, metric) => ({
        fps: acc.fps + metric.fps,
        scrollEvents: acc.scrollEvents + metric.scrollEvents,
        memoryUsage: acc.memoryUsage + (metric.memoryUsage || 0),
        renderTime: acc.renderTime + metric.renderTime,
        scrollLatency: acc.scrollLatency + metric.scrollLatency,
      }),
      { fps: 0, scrollEvents: 0, memoryUsage: 0, renderTime: 0, scrollLatency: 0 }
    );

    const count = this.metrics.length;
    return {
      fps: sum.fps / count,
      scrollEvents: sum.scrollEvents / count,
      memoryUsage: sum.memoryUsage / count,
      renderTime: sum.renderTime / count,
      scrollLatency: sum.scrollLatency / count,
    };
  }

  /**
   * 清除性能数据
   */
  public clearMetrics(): void {
    this.metrics = [];
    this.frameCount = 0;
    this.scrollEventCount = 0;
  }

  /**
   * 开始FPS监控
   */
  private startFPSMonitor(): void {
    const measureFPS = (currentTime: number) => {
      if (this.lastFrameTime === 0) {
        this.lastFrameTime = currentTime;
      }

      this.frameCount++;

      if (this.isMonitoring) {
        this.animationId = requestAnimationFrame(measureFPS);
      }
    };

    this.animationId = requestAnimationFrame(measureFPS);
  }

  /**
   * 开始滚动监控
   */
  private startScrollMonitor(): void {
    const handleScroll = () => {
      const currentTime = performance.now();
      this.scrollEventCount++;

      if (this.lastScrollTime > 0) {
        const latency = currentTime - this.lastScrollTime;
        this.log(`滚动延迟: ${latency.toFixed(2)}ms`);
      }

      this.lastScrollTime = currentTime;
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('touchmove', handleScroll, { passive: true });
  }

  /**
   * 开始渲染监控
   */
  private startRenderMonitor(): void {
    // 监控DOM变化
    const observer = new MutationObserver(() => {
      this.renderStartTime = performance.now();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
    });
  }

  /**
   * 收集性能指标
   */
  private collectMetrics(): void {
    if (!this.isMonitoring) return;

    const currentTime = performance.now();
    const timeDiff = currentTime - this.lastFrameTime;
    const fps = timeDiff > 0 ? (this.frameCount * 1000) / timeDiff : 0;

    const renderTime = this.renderStartTime > 0 ? currentTime - this.renderStartTime : 0;
    const scrollLatency = this.lastScrollTime > 0 ? currentTime - this.lastScrollTime : 0;

    const metrics: PerformanceMetrics = {
      fps: Math.round(fps),
      scrollEvents: this.scrollEventCount,
      renderTime: Math.round(renderTime),
      scrollLatency: Math.round(scrollLatency),
    };

    // 添加内存使用情况（如果支持）
    if (this.config.enableMemoryMonitor && 'memory' in performance) {
      const memory = (performance as any).memory;
      metrics.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024); // MB
    }

    // 添加安卓特定指标
    if (this.config.enableAndroidMonitor && this.isAndroidDevice) {
      metrics.androidVersion = this.androidVersion || undefined;
      metrics.isLowEndDevice = this.isLowEndDevice;
      metrics.scrollVelocity = this.lastScrollVelocity;

      // 检测GPU加速状态
      if (this.config.enableGPUMonitor) {
        metrics.gpuAcceleration = this.detectGPUAcceleration();
      }

      // 添加触摸延迟
      if (this.config.enableTouchMonitor && this.touchStartTime > 0) {
        metrics.touchLatency = Math.round(currentTime - this.touchStartTime);
      }
    }

    this.metrics.push(metrics);

    // 限制样本数量
    if (this.metrics.length > this.config.maxSamples) {
      this.metrics.shift();
    }

    // 重置计数器
    this.frameCount = 0;
    this.scrollEventCount = 0;
    this.lastFrameTime = currentTime;

    // 增强的日志输出，包含安卓特定信息
    let logMessage = `性能指标: FPS=${metrics.fps}, 滚动事件=${metrics.scrollEvents}, 渲染时间=${metrics.renderTime}ms`;

    if (this.isAndroidDevice && this.config.enableAndroidMonitor) {
      logMessage += `, 安卓版本=${metrics.androidVersion}, 低端设备=${metrics.isLowEndDevice}`;
      if (metrics.scrollVelocity !== undefined) {
        logMessage += `, 滚动速度=${metrics.scrollVelocity.toFixed(2)}`;
      }
      if (metrics.touchLatency !== undefined) {
        logMessage += `, 触摸延迟=${metrics.touchLatency}ms`;
      }
      if (metrics.gpuAcceleration !== undefined) {
        logMessage += `, GPU加速=${metrics.gpuAcceleration}`;
      }
    }

    this.log(logMessage);
  }

  /**
   * 检测GPU硬件加速状态
   */
  private detectGPUAcceleration(): boolean {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

      if (!gl) {
        return false;
      }

      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      if (debugInfo) {
        const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
        // 检查是否使用软件渲染
        return !renderer.toLowerCase().includes('software');
      }

      return true; // 如果无法确定，假设有GPU加速
    } catch (error) {
      return false;
    }
  }

  /**
   * 开始触摸事件监控
   */
  private startTouchMonitor(): void {
    if (!this.config.enableTouchMonitor || !this.isAndroidDevice) return;

    const handleTouchStart = (event: TouchEvent) => {
      this.touchStartTime = performance.now();
    };

    const handleTouchMove = (event: TouchEvent) => {
      if (event.touches.length === 1) {
        const touch = event.touches[0];
        const currentTime = performance.now();

        // 计算滚动速度
        if (this.lastScrollTime > 0) {
          const timeDiff = currentTime - this.lastScrollTime;
          if (timeDiff > 0) {
            // 简化的速度计算，实际应用中可能需要更复杂的算法
            this.lastScrollVelocity = 1000 / timeDiff; // 事件频率作为速度指标
          }
        }

        this.lastScrollTime = currentTime;
      }
    };

    const handleTouchEnd = () => {
      this.touchStartTime = 0;
      this.lastScrollVelocity = 0;
    };

    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });
    document.addEventListener('touchcancel', handleTouchEnd, { passive: true });
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 这里应该移除所有添加的事件监听器
    // 由于我们使用了匿名函数，实际实现中应该保存引用
  }

  /**
   * 调试日志
   */
  private log(message: string): void {
    if (this.config.debug) {
      console.log(`[PerformanceMonitor] ${message}`);
    }
  }

  /**
   * 生成性能报告
   */
  public generateReport(): string {
    const avg = this.getAverageMetrics();
    const latest = this.metrics[this.metrics.length - 1];

    return `
移动端性能报告
==============
平均FPS: ${avg.fps.toFixed(1)}
平均滚动事件数: ${avg.scrollEvents.toFixed(1)}
平均渲染时间: ${avg.renderTime.toFixed(1)}ms
平均滚动延迟: ${avg.scrollLatency.toFixed(1)}ms
${avg.memoryUsage ? `平均内存使用: ${avg.memoryUsage.toFixed(1)}MB` : ''}

当前FPS: ${latest?.fps || 0}
当前滚动事件数: ${latest?.scrollEvents || 0}
当前渲染时间: ${latest?.renderTime || 0}ms
当前滚动延迟: ${latest?.scrollLatency || 0}ms
${latest?.memoryUsage ? `当前内存使用: ${latest.memoryUsage}MB` : ''}

性能建议:
${avg.fps < 30 ? '⚠️ FPS过低，建议优化渲染性能' : '✅ FPS正常'}
${avg.scrollLatency > 16 ? '⚠️ 滚动延迟过高，建议优化滚动处理' : '✅ 滚动延迟正常'}
${avg.renderTime > 16 ? '⚠️ 渲染时间过长，建议优化DOM操作' : '✅ 渲染时间正常'}
${avg.memoryUsage && avg.memoryUsage > 100 ? '⚠️ 内存使用过高，建议检查内存泄漏' : '✅ 内存使用正常'}
    `.trim();
  }
}

/**
 * 创建性能监控实例
 */
export function createPerformanceMonitor(config?: PerformanceConfig): MobilePerformanceMonitor {
  return new MobilePerformanceMonitor(config);
}

/**
 * 快速性能检测
 */
export function quickPerformanceCheck(): Promise<PerformanceMetrics> {
  return new Promise((resolve) => {
    const monitor = new MobilePerformanceMonitor({
      sampleInterval: 1000,
      maxSamples: 1,
      debug: false,
    });

    monitor.start();

    setTimeout(() => {
      monitor.stop();
      const metrics = monitor.getMetrics();
      resolve(metrics[0] || {
        fps: 0,
        scrollEvents: 0,
        memoryUsage: 0,
        renderTime: 0,
        scrollLatency: 0,
      });
    }, 1000);
  });
}
