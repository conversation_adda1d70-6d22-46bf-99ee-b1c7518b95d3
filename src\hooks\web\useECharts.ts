import type { EChartsOption } from 'echarts';
import type { Ref } from 'vue';
import { useTimeoutFn } from '/@/hooks/core/useTimeout';
import { tryOnUnmounted } from '@vueuse/core';
import { unref, nextTick, watch, computed, ref } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { useEventListener } from '/@/hooks/event/useEventListener';
import { useBreakpoint } from '/@/hooks/event/useBreakpoint';
import echarts from '/@/utils/lib/echarts';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';

export function useECharts(
  elRef: Ref<HTMLDivElement>,
  theme: 'light' | 'dark' | 'default' = 'default',
) {
  const { getDarkMode: getSysDarkMode } = useRootSetting();

  const getDarkMode = computed(() => {
    return theme === 'default' ? getSysDarkMode.value : theme;
  });
  let chartInstance: echarts.ECharts | null = null;
  let resizeFn: Fn = resize;
  const cacheOptions = ref({}) as Ref<EChartsOption>;
  let removeResizeFn: Fn = () => { };

  // 移动端使用更长的防抖时间以提升性能
  const isMobile = () => typeof window !== 'undefined' && window.innerWidth <= 768;
  const isIOS = () => typeof window !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);

  // 根据设备类型调整防抖时间，iOS设备需要更长的防抖时间
  const debounceTime = isIOS() ? 300 : (isMobile() ? 200 : 100);
  resizeFn = useDebounceFn(resize, debounceTime);

  const getOptions = computed(() => {
    if (getDarkMode.value !== 'dark') {
      return cacheOptions.value as EChartsOption;
    }
    return {
      backgroundColor: 'transparent',
      ...cacheOptions.value,
    } as EChartsOption;
  });

  function initCharts(t = theme) {
    const el = unref(elRef);
    if (!el || !unref(el)) {
      return;
    }

    // 移动端优化配置
    const initOptions: any = {};
    if (isMobile()) {
      // iOS设备特殊优化
      if (isIOS()) {
        // iOS设备使用较低的设备像素比以提升性能
        initOptions.devicePixelRatio = 1;
        // iOS设备使用canvas渲染器，性能更好
        initOptions.renderer = 'canvas';
        // iOS设备禁用某些动画以提升性能
        initOptions.animation = false;
      } else {
        // 其他移动设备使用标准配置
        initOptions.renderer = 'canvas';
        initOptions.devicePixelRatio = window.devicePixelRatio > 2 ? 2 : window.devicePixelRatio;
      }
    }

    chartInstance = echarts.init(el, t, initOptions);
    const { removeEvent } = useEventListener({
      el: window,
      name: 'resize',
      listener: resizeFn,
    });
    removeResizeFn = removeEvent;
    const { widthRef, screenEnum } = useBreakpoint();
    if (unref(widthRef) <= screenEnum.MD || el.offsetHeight === 0) {
      useTimeoutFn(() => {
        resizeFn();
      }, 30);
    }
  }

  function setOptions(options: EChartsOption, clear = true) {
    // iOS设备优化选项
    if (isIOS() && options) {
      const optimizedOptions = { ...options };

      // iOS设备减少动画以提升性能
      if (optimizedOptions.animation !== false) {
        optimizedOptions.animation = true;
        optimizedOptions.animationDuration = 200; // 减少动画时间
        optimizedOptions.animationEasing = 'linear'; // 使用简单的缓动函数
      }

      // 优化工具提示
      if (optimizedOptions.tooltip) {
        optimizedOptions.tooltip = {
          ...optimizedOptions.tooltip,
          transitionDuration: 0.1,
          hideDelay: 0,
          enterable: false, // 禁用鼠标进入提示框
        };
      }

      cacheOptions.value = optimizedOptions;
    } else {
      cacheOptions.value = options;
    }

    if (unref(elRef)?.offsetHeight === 0) {
      const delay = isIOS() ? 50 : 30; // iOS设备使用更长的延迟
      useTimeoutFn(() => {
        setOptions(unref(getOptions));
      }, delay);
      return;
    }

    nextTick(() => {
      const delay = isIOS() ? 50 : 30; // iOS设备使用更长的延迟
      useTimeoutFn(() => {
        if (!chartInstance) {
          initCharts(getDarkMode.value as 'default');

          if (!chartInstance) return;
        }
        clear && chartInstance?.clear();

        chartInstance?.setOption(unref(getOptions));
      }, delay);
    });
  }

  function resize() {
    chartInstance?.resize();
  }

  watch(
    () => getDarkMode.value,
    (theme) => {
      if (chartInstance) {
        chartInstance.dispose();
        initCharts(theme as 'default');
        setOptions(cacheOptions.value);
      }
    },
  );

  tryOnUnmounted(() => {
    if (!chartInstance) return;
    removeResizeFn();
    chartInstance.dispose();
    chartInstance = null;
  });

  function getInstance(): echarts.ECharts | null {
    if (!chartInstance) {
      initCharts(getDarkMode.value as 'default');
    }
    return chartInstance;
  }

  return {
    setOptions,
    resize,
    echarts,
    getInstance,
  };
}
